import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "0.0.0.0", // Listen on all interfaces
    port: 3060,
    strictPort: true,
    open: true,
    cors: true,
    // Enable history API fallback for client-side routing
    // This ensures that refreshing pages like /clients, /invoices works correctly
    historyApiFallback: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        // Don't strip /api prefix - backend expects it
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('Proxy error:', err.message);
        },
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (_proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        }
      },
      // IMPORTANT: Only proxy actual API calls, not React Router navigation
      // Use more specific patterns to avoid interfering with client-side routing

      // Project API endpoints (only for API calls, not navigation)
      '^/projects/.*': {
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying Project API ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('Project API Proxy error:', err.message);
        },
      },

      // Client API endpoints (only for API calls, not navigation)
      '^/clients/.*': {
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying Client API ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('Client API Proxy error:', err.message);
        },
      },

      // Candidate API endpoints (only for API calls, not navigation)
      '^/candidates/.*': {
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying Candidate API ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('Candidate API Proxy error:', err.message);
        },
      },

      // SPOC API endpoints (only for API calls, not navigation)
      '^/spocs/.*': {
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying SPOC API ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('SPOC API Proxy error:', err.message);
        },
      },

      // BDM API endpoints (only for API calls, not navigation)
      '/bdms': {
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying BDM API ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('BDM API Proxy error:', err.message);
        },
      },
      '^/bdms/.*': {
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying BDM API ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('BDM API Proxy error:', err.message);
        },
      },
      // v1 endpoints - these don't have /api prefix in backend
      '/v1': {
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying v1 ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('v1 Proxy error:', err.message);
        },
      },
      // NOTE: These proxies are for API calls to public controllers (no /api prefix)
      // Frontend routes like /clients, /candidates, etc. are handled by React Router
      // Only actual API calls with paths like /clients/getAll, /candidates/123 will be proxied to the backend
      // Simple navigation to /clients, /candidates will be handled by React Router
    }
  },
  base: "/",
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
